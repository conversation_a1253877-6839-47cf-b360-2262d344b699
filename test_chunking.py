#!/usr/bin/env python3
"""
Test script to verify the improved chunking method works correctly.
"""

import sys
import os

# Add the app directory to the path so we can import the services
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from services.vector import VectorService
from services.database import <PERSON>Manager

def test_chunking():
    """Test the improved chunking method"""
    
    # Create a temporary database for testing
    db_manager = DatabaseManager(":memory:")
    vector_service = VectorService(db_manager)
    
    # Test text with sentences that could be broken up by character-based chunking
    test_text = """
    This is the first sentence of our test document. It contains important information about machine learning algorithms.
    The second sentence discusses natural language processing techniques. These techniques are used in many applications today.
    Here's a third sentence that talks about artificial intelligence and its impact on society. AI has revolutionized many industries.
    The fourth sentence mentions deep learning and neural networks. These are powerful tools for pattern recognition.
    Finally, this last sentence concludes our test document with some thoughts on the future of technology.
    """
    
    print("Original text:")
    print(test_text)
    print(f"\nOriginal text length: {len(test_text)} characters")
    
    # Test with different chunk sizes
    chunk_sizes = [100, 200, 300]
    
    for chunk_size in chunk_sizes:
        print(f"\n{'='*50}")
        print(f"Testing with chunk size: {chunk_size}")
        print(f"{'='*50}")
        
        chunks = vector_service._chunk_text(test_text.strip(), chunk_size)
        
        print(f"Number of chunks: {len(chunks)}")
        
        for i, chunk in enumerate(chunks):
            print(f"\nChunk {i+1} (length: {len(chunk)}):")
            print(f"'{chunk}'")
            
            # Check if chunk ends with a complete word/sentence
            if chunk and not chunk[-1].isspace():
                last_char = chunk[-1]
                if last_char.isalnum():
                    print(f"  ✓ Ends with complete word ('{last_char}')")
                elif last_char in '.!?':
                    print(f"  ✓ Ends with sentence punctuation ('{last_char}')")
                else:
                    print(f"  ? Ends with: '{last_char}'")
    
    # Test edge cases
    print(f"\n{'='*50}")
    print("Testing edge cases")
    print(f"{'='*50}")
    
    # Very short text
    short_text = "Short text."
    chunks = vector_service._chunk_text(short_text, 100)
    print(f"Short text chunks: {chunks}")
    
    # Text with no sentence endings
    no_sentences = "This is text without proper sentence endings and it goes on and on"
    chunks = vector_service._chunk_text(no_sentences, 50)
    print(f"No sentences chunks: {chunks}")
    
    # Text with abbreviations
    abbrev_text = "Dr. Smith works at Inc. Corp. He studies A.I. and machine learning. The U.S.A. is leading in tech."
    chunks = vector_service._chunk_text(abbrev_text, 50)
    print(f"Abbreviations chunks: {chunks}")

if __name__ == "__main__":
    test_chunking()
